import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import json
import re
import os
from urllib.parse import urljoin, urlparse
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


class EnhancedHousingScraper:
    def __init__(self, use_selenium=False):
        self.base_url = "https://tj.zu.anjuke.com/fangyuan/dongli-q-tjkgjjq/"
        self.session = requests.Session()
        self.data = []
        self.failed_attempts = 0
        self.use_selenium = use_selenium
        self.driver = None

        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

        # 更真实的User-Agent池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        # 增强的请求头
        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"'
        }

    def setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()

            # 反检测设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 窗口设置
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--start-maximized')

            # 禁用图片和CSS加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 随机User-Agent
            chrome_options.add_argument(f'--user-agent={random.choice(self.user_agents)}')

            self.driver = webdriver.Chrome(options=chrome_options)

            # 执行CDP命令来隐藏webdriver属性
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    })
                '''
            })

            self.logger.info("✅ Selenium WebDriver 初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ Selenium WebDriver 初始化失败: {e}")
            return False

    def get_random_headers(self, referer=None):
        """生成随机请求头"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)

        if referer:
            headers['Referer'] = referer
        else:
            headers['Referer'] = 'https://tj.zu.anjuke.com/'

        return headers

    def safe_delay(self, min_seconds=15, max_seconds=30):
        """安全延迟，更长的延迟时间"""
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.info(f"⏳ 延迟 {delay:.1f} 秒...")
        time.sleep(delay)

    def check_if_blocked(self, html, url):
        """检查是否被反爬虫阻止"""
        if not html or len(html) < 1000:
            self.logger.warning("❌ 页面内容过短，可能被阻止")
            return True

        blocked_indicators = [
            'antibot', 'verifycode', 'verify', '验证码', 'captcha',
            'callback.58.com', '访问过于频繁', '系统检测', 'Robot',
            'security check', '安全验证', 'cloudflare', 'Please wait'
        ]

        html_lower = html.lower()
        url_lower = url.lower() if url else ""

        for indicator in blocked_indicators:
            if indicator.lower() in html_lower or indicator.lower() in url_lower:
                self.logger.warning(f"🚫 检测到反爬虫机制: {indicator}")
                return True

        return False

    def get_page_with_selenium(self, url):
        """使用Selenium获取页面"""
        if not self.driver:
            if not self.setup_selenium_driver():
                return None, None

        try:
            self.logger.info(f"🌐 使用Selenium访问: {url}")

            # 访问页面
            self.driver.get(url)

            # 等待页面加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 模拟人类行为
            time.sleep(random.uniform(3, 7))

            # 随机滚动页面
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(random.uniform(2, 4))
            self.driver.execute_script("window.scrollTo(0, 0);")

            # 获取页面源码
            html = self.driver.page_source
            current_url = self.driver.current_url

            self.logger.info(f"📄 Selenium获取内容长度: {len(html)}")

            return html, current_url

        except TimeoutException:
            self.logger.error("❌ Selenium页面加载超时")
            return None, None
        except WebDriverException as e:
            self.logger.error(f"❌ Selenium WebDriver错误: {e}")
            return None, None
        except Exception as e:
            self.logger.error(f"❌ Selenium获取页面失败: {e}")
            return None, None

    def init_session(self):
        """初始化session with better approach"""
        self.logger.info("🔄 初始化session...")
        self.session.close()
        self.session = requests.Session()

        # 设置更多的session属性
        self.session.max_redirects = 10

        # 模拟浏览器行为 - 先访问主站
        try:
            headers = self.get_random_headers()

            # 先访问安居客主页
            main_response = self.session.get(
                'https://www.anjuke.com/',
                headers=headers,
                timeout=15,
                allow_redirects=True
            )

            if main_response.status_code == 200:
                self.safe_delay(3, 6)

                # 再访问天津租房首页
                tj_response = self.session.get(
                    'https://tj.zu.anjuke.com/',
                    headers=self.get_random_headers('https://www.anjuke.com/'),
                    timeout=15,
                    allow_redirects=True
                )

                if tj_response.status_code == 200:
                    self.logger.info("✅ Session初始化成功")
                    self.safe_delay(2, 5)
                    return True

        except Exception as e:
            self.logger.error(f"❌ Session初始化失败: {e}")

        return False

    def get_page_content(self, page_num):
        """获取页面内容 - 支持requests和selenium两种方式"""
        # 构建URL
        if page_num == 1:
            url = self.base_url
        else:
            url = f"{self.base_url}p{page_num}/"

        # 如果启用了selenium或者requests方式失败次数过多，使用selenium
        if self.use_selenium or self.failed_attempts >= 2:
            return self.get_page_with_selenium(url)

        # 使用requests方式
        return self.get_page_with_requests(url, page_num)

    def get_page_with_requests(self, url, page_num):
        """使用requests获取页面"""
        self.logger.info(f"🌐 使用requests访问第{page_num}页: {url}")

        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 设置referer
                if page_num > 1:
                    referer = f"{self.base_url}p{page_num - 1}/" if page_num > 2 else self.base_url
                else:
                    referer = "https://tj.zu.anjuke.com/"

                headers = self.get_random_headers(referer)

                # 发送请求
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=30,
                    allow_redirects=True
                )

                self.logger.info(f"📊 状态码: {response.status_code}, URL: {response.url}")
                self.logger.info(f"📄 内容长度: {len(response.text)}")

                # 检查是否被阻止
                if self.check_if_blocked(response.text, response.url):
                    if attempt < max_retries - 1:
                        self.logger.warning(f"🔄 第{attempt + 1}次尝试被阻止，等待后重试...")
                        self.safe_delay(60, 120)  # 更长的延迟
                        # 重新初始化session
                        self.init_session()
                        continue
                    else:
                        self.logger.error("❌ requests方式多次尝试仍被阻止，切换到Selenium")
                        # 切换到selenium模式
                        self.use_selenium = True
                        return self.get_page_with_selenium(url)

                if response.status_code == 200:
                    self.logger.info("✅ 页面获取成功")
                    return response.text, response.url
                else:
                    self.logger.warning(f"⚠️ HTTP状态码异常: {response.status_code}")

            except Exception as e:
                self.logger.error(f"❌ 第{attempt + 1}次请求失败: {e}")

            # 重试前延迟
            if attempt < max_retries - 1:
                self.safe_delay(30, 60)

        return None, None

    def parse_house_list(self, html):
        """解析房屋列表"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')

        # 调试信息
        title = soup.title.text.strip() if soup.title else "无标题"
        self.logger.info(f"📄 页面标题: {title}")

        # 尝试多种选择器找房源条目
        selectors = [
            '.zu-itemmod',  # 主要选择器
            '.list-item',  # 移动端
            '.item-mod',  # 备用
            '.rent-item',  # 备用
            '[data-item-id]',  # 有ID的项目
            '.item',  # 通用
            '.house-item',  # 新版本可能的选择器
            '.property-item'  # 通用房源项
        ]

        house_items = []
        for selector in selectors:
            house_items = soup.select(selector)
            if house_items:
                self.logger.info(f"✅ 使用选择器 '{selector}' 找到 {len(house_items)} 个房源")
                break

        if not house_items:
            self.logger.error("❌ 未找到房源条目")
            # 保存HTML用于调试
            self.save_debug_html(html)
            return []

        houses = []
        for idx, item in enumerate(house_items):
            try:
                house_data = self.extract_house_info(item, idx + 1)
                if house_data:
                    houses.append(house_data)
                    self.logger.info(f"✅ 解析房源 {idx + 1}: {house_data['title'][:30]}...")

            except Exception as e:
                self.logger.error(f"❌ 解析第{idx + 1}个房源失败: {e}")
                continue

        return houses

    def extract_house_info(self, item, index):
        """提取单个房源信息"""
        house_data = {}

        try:
            # 标题和链接 - 多种选择器
            title_selectors = ['h3 a', '.house-title a', 'a[title]', '.title a', '.property-content-title a']
            title_elem = None

            for selector in title_selectors:
                title_elem = item.select_one(selector)
                if title_elem:
                    break

            if not title_elem:
                self.logger.warning(f"❌ 第{index}个房源未找到标题")
                return None

            house_data['title'] = title_elem.get_text(strip=True)

            # 处理链接
            link = title_elem.get('href', '')
            if link:
                if link.startswith('//'):
                    link = 'https:' + link
                elif link.startswith('/'):
                    link = 'https://tj.zu.anjuke.com' + link
                elif not link.startswith('http'):
                    link = urljoin(self.base_url, link)
            house_data['link'] = link

            # 房屋详情信息
            info_selectors = ['.details-item', '.zu-info', '.house-info', '.desc', '.property-content-info']
            house_data['house_info'] = self.extract_text_by_selectors(item, info_selectors)

            # 位置信息
            location_selectors = ['.comm-address', '.address', '.location', '.area',
                                  '.property-content-info-comm-address']
            house_data['location'] = self.extract_text_by_selectors(item, location_selectors)

            # 价格信息
            price_text = self.extract_price_info(item)
            house_data['price'] = price_text

            # 标签信息
            tag_selectors = ['.tags-bottom span', '.tags span', '.tag', '.property-content-info-tag span']
            tags = []
            for selector in tag_selectors:
                tag_elems = item.select(selector)
                if tag_elems:
                    tags = [tag.get_text(strip=True) for tag in tag_elems if tag.get_text(strip=True)]
                    break
            house_data['tags'] = ', '.join(tags)

            # 确保必要字段存在
            if not house_data['title']:
                return None

            return house_data

        except Exception as e:
            self.logger.error(f"❌ 提取房源信息出错: {e}")
            return None

    def extract_text_by_selectors(self, item, selectors):
        """通过多个选择器提取文本"""
        for selector in selectors:
            elem = item.select_one(selector)
            if elem:
                text = elem.get_text(strip=True)
                if text:
                    return text
        return ""

    def extract_price_info(self, item):
        """提取价格信息"""
        # 价格选择器
        price_selectors = [
            '.zu-side strong',
            '.price strong',
            '.price',
            '.money',
            '.rent-price',
            '.property-price strong'
        ]

        price_text = ""
        for selector in price_selectors:
            price_elem = item.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                break

        # 单位选择器
        unit_selectors = ['.unit', '.price-unit']
        unit_text = ""
        for selector in unit_selectors:
            unit_elem = item.select_one(selector)
            if unit_elem:
                unit_text = unit_elem.get_text(strip=True)
                break

        if price_text:
            if unit_text and unit_text not in price_text:
                return f"{price_text}{unit_text}"
            else:
                return price_text
        else:
            return "价格面议"

    def save_debug_html(self, html):
        """保存HTML用于调试"""
        try:
            debug_dir = "debug"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = int(time.time())
            filename = f"debug_{timestamp}.html"
            filepath = os.path.join(debug_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html)
            self.logger.info(f"🐛 调试HTML已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"❌ 保存调试HTML失败: {e}")

    def scrape_houses(self, max_pages=5):
        """爬取房源信息"""
        self.logger.info(f"🏠 开始爬取房源信息，最多 {max_pages} 页")
        print("=" * 60)

        # 初始化session（如果不使用selenium）
        if not self.use_selenium:
            if not self.init_session():
                self.logger.warning("❌ Session初始化失败，切换到Selenium模式")
                self.use_selenium = True

        successful_pages = 0
        failed_pages = []

        for page_num in range(1, max_pages + 1):
            self.logger.info(f"\n📄 正在处理第 {page_num}/{max_pages} 页")
            print("-" * 50)

            # 获取页面内容
            html, final_url = self.get_page_content(page_num)

            if html:
                # 解析房源信息
                houses = self.parse_house_list(html)

                if houses:
                    self.data.extend(houses)
                    successful_pages += 1
                    self.logger.info(f"✅ 第 {page_num} 页成功解析 {len(houses)} 条房源")
                    self.logger.info(f"📊 累计获取 {len(self.data)} 条房源信息")
                    self.failed_attempts = 0
                else:
                    self.logger.error(f"❌ 第 {page_num} 页未获取到房源信息")
                    failed_pages.append(page_num)
                    self.failed_attempts += 1
            else:
                self.logger.error(f"❌ 第 {page_num} 页获取失败")
                failed_pages.append(page_num)
                self.failed_attempts += 1

            # 如果连续失败太多，提前退出
            if self.failed_attempts >= 5:
                self.logger.error(f"🛑 连续失败 {self.failed_attempts} 次，可能被限制访问")
                break

            # 页面间延迟
            if page_num < max_pages:
                self.safe_delay(30, 60)  # 更长的延迟

        # 结果统计
        self.logger.info(f"\n🎉 爬取完成！")
        self.logger.info(f"📊 成功页面: {successful_pages}/{max_pages}")
        self.logger.info(f"📊 总获取房源: {len(self.data)} 条")

        if failed_pages:
            self.logger.info(f"❌ 失败页面: {failed_pages}")

        return self.data

    def save_data(self, csv_filename=None, excel_filename=None):
        """保存数据到文件"""
        if not self.data:
            self.logger.warning("⚠️ 没有数据可保存")
            return False

        timestamp = time.strftime("%Y%m%d_%H%M%S")

        # 保存CSV
        if csv_filename is None:
            csv_filename = f"tianjin_houses_{timestamp}.csv"

        try:
            df = pd.DataFrame(self.data)
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"💾 CSV数据已保存到: {csv_filename}")
        except Exception as e:
            self.logger.error(f"❌ 保存CSV失败: {e}")

        # 保存Excel
        if excel_filename is None:
            excel_filename = f"tianjin_houses_{timestamp}.xlsx"

        try:
            df = pd.DataFrame(self.data)
            df.to_excel(excel_filename, index=False)
            self.logger.info(f"💾 Excel数据已保存到: {excel_filename}")
        except Exception as e:
            self.logger.error(f"❌ 保存Excel失败: {e}")

        return True

    def show_statistics(self):
        """显示数据统计信息"""
        if not self.data:
            self.logger.warning("❌ 没有数据可统计")
            return

        self.logger.info(f"\n📈 数据统计:")
        self.logger.info(f"   📊 总房源数量: {len(self.data)}")

        # 价格统计
        prices = []
        for house in self.data:
            price_str = house.get('price', '')
            if price_str and price_str != '价格面议':
                # 提取数字价格
                price_match = re.search(r'(\d+)', price_str)
                if price_match:
                    try:
                        price = int(price_match.group(1))
                        if 100 <= price <= 50000:  # 合理价格范围
                            prices.append(price)
                    except:
                        continue

        if prices:
            self.logger.info(f"   💰 平均租金: {sum(prices) / len(prices):.0f} 元/月")
            self.logger.info(f"   💰 最低租金: {min(prices)} 元/月")
            self.logger.info(f"   💰 最高租金: {max(prices)} 元/月")
            self.logger.info(f"   💰 有效价格样本: {len(prices)} 个")
        else:
            self.logger.info("   💰 未能解析到有效价格信息")

        # 显示前3条示例
        self.logger.info(f"\n📋 数据示例:")
        for i, house in enumerate(self.data[:3]):
            self.logger.info(f"   {i + 1}. {house.get('title', 'N/A')[:40]}... - {house.get('price', 'N/A')}")

    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("✅ Selenium WebDriver 已关闭")
            except Exception as e:
                self.logger.error(f"❌ 关闭WebDriver失败: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()


def main():
    """主函数"""
    print("🏠 天津房源信息爬虫 - 增强版")
    print("=" * 60)

    # 询问用户是否直接使用Selenium
    use_selenium = input("是否直接使用Selenium模式？(y/n，默认n): ").lower().strip() == 'y'

    scraper = EnhancedHousingScraper(use_selenium=use_selenium)

    try:
        # 爬取数据
        houses = scraper.scrape_houses(max_pages=100)  # 减少页数以降低被封概率

        if houses:
            # 保存数据
            scraper.save_data()

            # 显示统计
            scraper.show_statistics()
        else:
            print("❌ 未获取到任何房源数据")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        if scraper.data:
            print("💾 正在保存已获取的数据...")
            scraper.save_data()
            scraper.show_statistics()

    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        if scraper.data:
            print("💾 正在保存已获取的数据...")
            scraper.save_data()

    finally:
        # 清理资源
        scraper.cleanup()

    print("\n🎉 程序执行完毕！")


if __name__ == "__main__":
    main()