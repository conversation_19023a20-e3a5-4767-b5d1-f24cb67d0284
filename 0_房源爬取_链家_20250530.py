import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import json
import re
import os
from urllib.parse import urljoin, urlparse
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from selenium.webdriver.common.action_chains import ActionChains


class AntiCaptchaHousingScraper:
    def __init__(self):
        # 切换到链家，相对来说反爬虫较弱
        self.base_url = "https://tj.lianjia.com/zufang/konggangjingjiqu/"
        self.backup_urls = [
            "https://tj.anjuke.com/sale/konggangjingjiqu/",  # 安居客备用
            "https://tj.fang.com/house/",  # 房天下备用
        ]

        self.session = requests.Session()
        self.data = []
        self.failed_attempts = 0
        self.driver = None
        self.images_enabled = False  # 跟踪图片是否启用

        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

        # 增强的User-Agent池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0'
        ]

    def setup_selenium_driver(self):
        """设置增强的Selenium WebDriver"""
        try:
            chrome_options = Options()

            # 基础反检测设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 增强反检测
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript')  # 暂时禁用JS来避免检测

            # 窗口设置
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--start-maximized')

            # 性能优化
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.media_stream": 2,
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 随机User-Agent
            user_agent = random.choice(self.user_agents)
            chrome_options.add_argument(f'--user-agent={user_agent}')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)

            # 高级反检测脚本
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    // 隐藏webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // 修改plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [{filename: "internal-pdf-viewer", description: "Portable Document Format", name: "Chrome PDF Plugin"}],
                    });

                    // 修改languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ["zh-CN", "zh", "en-US", "en"],
                    });

                    // 隐藏chrome runtime
                    window.chrome = {
                        runtime: {}
                    };

                    // 修改permissions
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                    );
                '''
            })

            self.logger.info("✅ 增强版 Selenium WebDriver 初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ Selenium WebDriver 初始化失败: {e}")
            return False

    def enable_images_for_captcha(self):
        """为验证码启用图片显示"""
        try:
            if self.images_enabled:
                return True

            self.logger.info("🖼️ 启用图片显示以便查看验证码...")

            # 通过JavaScript启用图片
            self.driver.execute_script("""
                // 移除禁用图片的样式
                var styles = document.querySelectorAll('style');
                styles.forEach(function(style) {
                    if (style.innerHTML.includes('img') && style.innerHTML.includes('display: none')) {
                        style.remove();
                    }
                });

                // 启用图片显示
                var style = document.createElement('style');
                style.innerHTML = `
                    img {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                `;
                document.head.appendChild(style);

                // 重新加载所有图片
                var images = document.getElementsByTagName('img');
                for(var i = 0; i < images.length; i++) {
                    if (images[i].src) {
                        var src = images[i].src;
                        images[i].src = '';
                        images[i].src = src;
                    }
                    // 如果有data-src属性，也加载
                    if (images[i].getAttribute('data-src')) {
                        images[i].src = images[i].getAttribute('data-src');
                    }
                }

                // 触发图片加载事件
                window.dispatchEvent(new Event('load'));
            """)

            self.images_enabled = True
            self.logger.info("✅ 图片显示已启用")
            return True

        except Exception as e:
            self.logger.error(f"❌ 启用图片显示失败: {e}")
            return False

    def handle_captcha_page(self):
        """处理验证码页面"""
        try:
            self.logger.info("🔍 检测到可能的验证码页面，尝试处理...")

            # 启用图片显示以便用户看到验证码
            self.enable_images_for_captcha()

            # 刷新页面以确保图片加载
            self.driver.refresh()
            time.sleep(5)

            # 等待用户手动处理验证码
            self.logger.info("⏳ 请手动完成验证码验证，程序将等待90秒...")
            self.logger.info("💡 提示：验证码图片现在应该可以正常显示了")

            # 分段等待，每30秒检查一次
            for i in range(3):
                self.logger.info(f"⏳ 等待中... ({(i+1)*30}/90秒)")
                time.sleep(30)

                # 检查是否成功跳过验证码
                current_url = self.driver.current_url
                if "verifycode" not in current_url.lower() and "captcha" not in current_url.lower():
                    self.logger.info("✅ 验证码验证成功！")
                    return True

            self.logger.warning("⚠️ 验证码可能未完成，继续尝试...")
            return False

        except Exception as e:
            self.logger.error(f"❌ 处理验证码失败: {e}")
            return False

    def try_different_entry_points(self):
        """尝试不同的入口点"""
        entry_points = [
            "https://tj.lianjia.com/",  # 链家首页
            "https://tj.lianjia.com/zufang/",  # 租房首页
            "https://www.lianjia.com/",  # 全国首页
        ]

        for entry in entry_points:
            try:
                self.logger.info(f"🚪 尝试从入口点进入: {entry}")
                self.driver.get(entry)
                time.sleep(random.uniform(5, 10))

                # 模拟用户行为
                self.simulate_real_user_behavior()

                # 现在尝试访问目标页面
                self.driver.get(self.base_url)
                time.sleep(random.uniform(3, 7))

                # 检查是否成功
                if not self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                    self.logger.info("✅ 成功通过入口点访问！")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 入口点 {entry} 失败: {e}")
                continue

        return False

    def simulate_real_user_behavior(self):
        """模拟真实用户行为"""
        try:
            # 随机移动鼠标
            actions = ActionChains(self.driver)

            # 模拟搜索行为
            try:
                search_box = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder*='请输入']")
                if search_box:
                    search_box.click()
                    time.sleep(random.uniform(1, 3))
                    search_box.send_keys("天津")
                    time.sleep(random.uniform(2, 4))
                    search_box.clear()
            except:
                pass

            # 随机滚动
            for _ in range(3):
                scroll_y = random.randint(200, 800)
                self.driver.execute_script(f"window.scrollTo(0, {scroll_y});")
                time.sleep(random.uniform(1, 3))

            # 回到顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(2, 4))

        except Exception as e:
            self.logger.warning(f"⚠️ 模拟用户行为失败: {e}")

    def is_blocked_page(self, html, url):
        """检查是否被阻止"""
        if not html or len(html) < 1000:
            return True

        blocked_indicators = [
            'verifycode', 'captcha', '验证码', '安全验证',
            'antibot', 'robot', '访问异常', '系统检测',
            'cloudflare', 'please wait', '稍后再试'
        ]

        html_lower = html.lower()
        url_lower = url.lower() if url else ""

        for indicator in blocked_indicators:
            if indicator.lower() in html_lower or indicator.lower() in url_lower:
                return True
        return False

    def get_page_with_selenium(self, url):
        """使用Selenium获取页面"""
        if not self.driver:
            if not self.setup_selenium_driver():
                return None, None

        try:
            self.logger.info(f"🌐 访问页面: {url}")

            # 尝试不同的访问策略
            success = False

            # 策略1: 直接访问
            try:
                self.driver.get(url)
                time.sleep(random.uniform(8, 15))

                if not self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                    success = True
                else:
                    self.logger.warning("🚫 直接访问被阻止，尝试其他策略...")
            except:
                pass

            # 策略2: 通过入口点访问
            if not success:
                success = self.try_different_entry_points()

            # 策略3: 处理验证码
            if not success and self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                if "verifycode" in self.driver.current_url.lower() or "captcha" in self.driver.page_source.lower():
                    success = self.handle_captcha_page()

            if success:
                # 模拟用户浏览行为
                self.simulate_real_user_behavior()

                html = self.driver.page_source
                current_url = self.driver.current_url

                self.logger.info(f"📄 获取内容长度: {len(html)}")
                return html, current_url
            else:
                return None, None

        except Exception as e:
            self.logger.error(f"❌ Selenium获取页面失败: {e}")
            return None, None

    def parse_house_list_lianjia(self, html):
        """解析链家房源列表"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')

        # 调试信息
        title = soup.title.text.strip() if soup.title else "无标题"
        self.logger.info(f"📄 页面标题: {title}")

        # 链家的选择器
        selectors = [
            '.content__list .content__list--item',  # 链家主要选择器
            '.rent-list-wrap .content__list--item',
            '.content__article',
            'div[data-el="rent"]'
        ]

        house_items = []
        for selector in selectors:
            house_items = soup.select(selector)
            if house_items:
                self.logger.info(f"✅ 使用选择器 '{selector}' 找到 {len(house_items)} 个房源")
                break

        if not house_items:
            self.logger.error("❌ 未找到房源条目")
            self.analyze_page_structure(soup)
            self.save_debug_html(html, "lianjia")
            return []

        houses = []
        for idx, item in enumerate(house_items):
            try:
                house_data = self.extract_house_info_lianjia(item, idx + 1)
                if house_data:
                    houses.append(house_data)
                    self.logger.info(f"✅ 解析房源 {idx + 1}: {house_data.get('title', 'N/A')[:30]}...")

            except Exception as e:
                self.logger.error(f"❌ 解析第{idx + 1}个房源失败: {e}")
                continue

        return houses

    def extract_house_info_lianjia(self, item, index):
        """提取链家房源信息"""
        house_data = {}

        try:
            # 标题和链接
            title_elem = item.select_one('.content__list--item--title a')
            if not title_elem:
                title_elem = item.select_one('a[title]')

            if not title_elem:
                return None

            house_data['title'] = title_elem.get_text(strip=True)

            # 链接
            link = title_elem.get('href', '')
            if link and not link.startswith('http'):
                link = 'https://tj.lianjia.com' + link
            house_data['link'] = link

            # 房屋信息
            house_info_elem = item.select_one('.content__list--item--des')
            house_data['house_info'] = house_info_elem.get_text(strip=True) if house_info_elem else ""

            # 位置
            location_elem = item.select_one('.content__list--item--des a')
            house_data['location'] = location_elem.get_text(strip=True) if location_elem else ""

            # 价格
            price_elem = item.select_one('.content__list--item-price em')
            if price_elem:
                price = price_elem.get_text(strip=True)
                house_data['price'] = f"{price}元/月" if price else "价格面议"
            else:
                house_data['price'] = "价格面议"

            # 标签
            tag_elems = item.select('.content__list--item--bottom__tag')
            tags = [tag.get_text(strip=True) for tag in tag_elems]
            house_data['tags'] = ', '.join(tags)

            return house_data

        except Exception as e:
            self.logger.error(f"❌ 提取房源信息出错: {e}")
            return None

    def analyze_page_structure(self, soup):
        """分析页面结构"""
        self.logger.info("🔍 分析页面结构...")

        # 查找可能的容器
        potential_containers = [
            'div[class*="item"]',
            'div[class*="list"]',
            'div[class*="content"]',
            'li[class*="item"]'
        ]

        for selector in potential_containers:
            elements = soup.select(selector)
            if elements:
                self.logger.info(f"   找到 {len(elements)} 个 '{selector}' 元素")

    def save_debug_html(self, html, prefix="debug"):
        """保存调试HTML"""
        try:
            debug_dir = "debug"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = int(time.time())
            filename = f"{prefix}_{timestamp}.html"
            filepath = os.path.join(debug_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html)
            self.logger.info(f"🐛 调试HTML已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"❌ 保存调试HTML失败: {e}")

    def scrape_houses(self, max_pages=3):
        """爬取房源信息"""
        self.logger.info(f"🏠 开始爬取链家房源信息，最多 {max_pages} 页")
        print("=" * 60)

        successful_pages = 0
        failed_pages = []

        for page_num in range(1, max_pages + 1):
            self.logger.info(f"\n📄 正在处理第 {page_num}/{max_pages} 页")
            print("-" * 50)

            # 构建URL
            if page_num == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}pg{page_num}/"

            # 获取页面内容
            html, final_url = self.get_page_with_selenium(url)

            if html:
                # 解析房源信息
                houses = self.parse_house_list_lianjia(html)

                if houses:
                    self.data.extend(houses)
                    successful_pages += 1
                    self.logger.info(f"✅ 第 {page_num} 页成功解析 {len(houses)} 条房源")
                    self.logger.info(f"📊 累计获取 {len(self.data)} 条房源信息")
                    self.failed_attempts = 0
                else:
                    self.logger.error(f"❌ 第 {page_num} 页未获取到房源信息")
                    failed_pages.append(page_num)
                    self.failed_attempts += 1
            else:
                self.logger.error(f"❌ 第 {page_num} 页获取失败")
                failed_pages.append(page_num)
                self.failed_attempts += 1

            # 如果连续失败，提前退出
            if self.failed_attempts >= 2:
                self.logger.error(f"🛑 连续失败 {self.failed_attempts} 次，停止爬取")
                break

            # 页面间延迟
            if page_num < max_pages:
                delay = random.uniform(20, 40)
                self.logger.info(f"⏳ 延迟 {delay:.1f} 秒...")
                time.sleep(delay)

        # 结果统计
        self.logger.info(f"\n🎉 爬取完成！")
        self.logger.info(f"📊 成功页面: {successful_pages}/{max_pages}")
        self.logger.info(f"📊 总获取房源: {len(self.data)} 条")

        return self.data

    def save_data(self, filename_prefix="tianjin_houses_lianjia"):
        """保存数据"""
        if not self.data:
            self.logger.warning("⚠️ 没有数据可保存")
            return False

        timestamp = time.strftime("%Y%m%d_%H%M%S")

        # 保存CSV
        csv_filename = f"{filename_prefix}_{timestamp}.csv"
        try:
            df = pd.DataFrame(self.data)
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"💾 数据已保存到: {csv_filename}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 保存数据失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("✅ WebDriver 已关闭")
            except Exception as e:
                self.logger.error(f"❌ 关闭WebDriver失败: {e}")


def main():
    """主函数"""
    print("🏠 防验证码房源爬虫 - 链家版")
    print("=" * 60)
    print("💡 如果遇到验证码，程序会等待您手动完成验证")
    print("=" * 60)

    scraper = AntiCaptchaHousingScraper()

    try:
        # 爬取数据
        houses = scraper.scrape_houses(max_pages=50)

        if houses:
            scraper.save_data()
            print(f"\n✅ 成功获取 {len(houses)} 条房源信息！")
        else:
            print("❌ 未获取到任何房源数据")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        scraper.cleanup()

    print("\n🎉 程序执行完毕！")


if __name__ == "__main__":
    main()