import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import json
import re
import os
from urllib.parse import urljoin, urlparse
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from selenium.webdriver.common.action_chains import ActionChains


class AntiCaptchaHousingScraper:
    def __init__(self):
        # 切换到链家，相对来说反爬虫较弱
        self.base_url = "https://tj.lianjia.com/zufang/konggangjingjiqu/"
        self.backup_urls = [
            "https://tj.anjuke.com/sale/konggangjingjiqu/",  # 安居客备用
            "https://tj.fang.com/house/",  # 房天下备用
        ]

        self.session = requests.Session()
        self.data = []
        self.failed_attempts = 0
        self.driver = None
        self.images_enabled = False  # 跟踪图片是否启用
        self.verified_session = False  # 跟踪是否已通过验证
        self.last_successful_url = None  # 记录最后成功的URL

        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

        # 增强的User-Agent池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0'
        ]

    def setup_selenium_driver(self):
        """设置增强的Selenium WebDriver"""
        try:
            chrome_options = Options()

            # 基础反检测设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 增强反检测
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript')  # 暂时禁用JS来避免检测

            # 窗口设置
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--start-maximized')

            # 性能优化
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.media_stream": 2,
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 随机User-Agent
            user_agent = random.choice(self.user_agents)
            chrome_options.add_argument(f'--user-agent={user_agent}')

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)

            # 高级反检测脚本
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': '''
                    // 隐藏webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // 修改plugins
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [{filename: "internal-pdf-viewer", description: "Portable Document Format", name: "Chrome PDF Plugin"}],
                    });

                    // 修改languages
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ["zh-CN", "zh", "en-US", "en"],
                    });

                    // 隐藏chrome runtime
                    window.chrome = {
                        runtime: {}
                    };

                    // 修改permissions
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                    );
                '''
            })

            self.logger.info("✅ 增强版 Selenium WebDriver 初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ Selenium WebDriver 初始化失败: {e}")
            return False

    def enable_images_for_captcha(self):
        """为验证码启用图片显示"""
        try:
            if self.images_enabled:
                return True

            self.logger.info("🖼️ 启用图片显示以便查看验证码...")

            # 通过JavaScript启用图片
            self.driver.execute_script("""
                // 移除禁用图片的样式
                var styles = document.querySelectorAll('style');
                styles.forEach(function(style) {
                    if (style.innerHTML.includes('img') && style.innerHTML.includes('display: none')) {
                        style.remove();
                    }
                });

                // 启用图片显示
                var style = document.createElement('style');
                style.innerHTML = `
                    img {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                `;
                document.head.appendChild(style);

                // 重新加载所有图片
                var images = document.getElementsByTagName('img');
                for(var i = 0; i < images.length; i++) {
                    if (images[i].src) {
                        var src = images[i].src;
                        images[i].src = '';
                        images[i].src = src;
                    }
                    // 如果有data-src属性，也加载
                    if (images[i].getAttribute('data-src')) {
                        images[i].src = images[i].getAttribute('data-src');
                    }
                }

                // 触发图片加载事件
                window.dispatchEvent(new Event('load'));
            """)

            self.images_enabled = True
            self.logger.info("✅ 图片显示已启用")
            return True

        except Exception as e:
            self.logger.error(f"❌ 启用图片显示失败: {e}")
            return False

    def handle_captcha_page(self):
        """处理验证码页面"""
        try:
            self.logger.info("🔍 检测到可能的验证码页面，尝试处理...")

            # 启用图片显示以便用户看到验证码
            self.enable_images_for_captcha()

            # 刷新页面以确保图片加载
            self.driver.refresh()
            time.sleep(5)

            # 等待用户手动处理验证码
            self.logger.info("⏳ 请手动完成验证码验证，程序将等待90秒...")
            self.logger.info("💡 提示：验证码图片现在应该可以正常显示了")

            # 分段等待，每30秒检查一次
            for i in range(3):
                self.logger.info(f"⏳ 等待中... ({(i+1)*30}/90秒)")
                time.sleep(30)

                # 检查是否成功跳过验证码
                current_url = self.driver.current_url
                if "verifycode" not in current_url.lower() and "captcha" not in current_url.lower():
                    self.logger.info("✅ 验证码验证成功！")
                    self.verified_session = True  # 标记已通过验证
                    self.last_successful_url = current_url
                    return True

            self.logger.warning("⚠️ 验证码可能未完成，继续尝试...")
            return False

        except Exception as e:
            self.logger.error(f"❌ 处理验证码失败: {e}")
            return False

    def try_different_entry_points(self):
        """尝试不同的入口点"""
        entry_points = [
            "https://tj.lianjia.com/",  # 链家首页
            "https://tj.lianjia.com/zufang/",  # 租房首页
            "https://www.lianjia.com/",  # 全国首页
        ]

        for entry in entry_points:
            try:
                self.logger.info(f"🚪 尝试从入口点进入: {entry}")
                self.driver.get(entry)
                time.sleep(random.uniform(5, 10))

                # 模拟用户行为
                self.simulate_real_user_behavior()

                # 现在尝试访问目标页面
                self.driver.get(self.base_url)
                time.sleep(random.uniform(3, 7))

                # 检查是否成功
                if not self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                    self.logger.info("✅ 成功通过入口点访问！")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 入口点 {entry} 失败: {e}")
                continue

        return False

    def simulate_real_user_behavior(self):
        """模拟真实用户行为"""
        try:
            self.logger.info("🎭 模拟真实用户浏览行为...")
            actions = ActionChains(self.driver)

            # 1. 随机移动鼠标到空白区域并点击
            window_width = self.driver.execute_script("return window.innerWidth;")
            window_height = self.driver.execute_script("return window.innerHeight;")

            for _ in range(random.randint(2, 4)):
                # 移动到随机空白位置
                x = random.randint(100, window_width - 100)
                y = random.randint(100, window_height - 100)

                actions.move_by_offset(x - window_width//2, y - window_height//2)
                time.sleep(random.uniform(0.5, 1.5))

                # 随机点击
                if random.choice([True, False]):
                    actions.click()
                    time.sleep(random.uniform(0.3, 0.8))

                actions.perform()
                actions = ActionChains(self.driver)  # 重置ActionChains
                time.sleep(random.uniform(1, 2))

            # 2. 滚轮滚动 - 模拟真实浏览
            scroll_actions = [
                # 向下滚动
                lambda: self.driver.execute_script("window.scrollBy(0, arguments[0]);", random.randint(200, 500)),
                # 向上滚动
                lambda: self.driver.execute_script("window.scrollBy(0, arguments[0]);", -random.randint(100, 300)),
                # 滚动到随机位置
                lambda: self.driver.execute_script("window.scrollTo(0, arguments[0]);", random.randint(0, 1000)),
            ]

            for _ in range(random.randint(3, 6)):
                scroll_action = random.choice(scroll_actions)
                scroll_action()
                time.sleep(random.uniform(1, 3))

            # 3. 模拟鼠标悬停在房源列表上
            try:
                house_items = self.driver.find_elements(By.CSS_SELECTOR, ".content__list--item")
                if house_items:
                    random_item = random.choice(house_items[:5])  # 选择前5个中的随机一个
                    actions.move_to_element(random_item)
                    actions.perform()
                    time.sleep(random.uniform(1, 2))
            except:
                pass

            # 4. 模拟搜索行为（偶尔）
            if random.choice([True, False]):
                try:
                    search_box = self.driver.find_element(By.CSS_SELECTOR, "input[placeholder*='请输入'], input[placeholder*='搜索']")
                    if search_box:
                        actions.move_to_element(search_box)
                        actions.click()
                        actions.perform()
                        time.sleep(random.uniform(1, 2))

                        # 输入一些字符然后删除
                        search_terms = ["天津", "租房", "空港"]
                        term = random.choice(search_terms)
                        search_box.send_keys(term)
                        time.sleep(random.uniform(2, 4))
                        search_box.clear()
                        time.sleep(random.uniform(1, 2))
                except:
                    pass

            # 5. 最后滚动到页面中部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);")
            time.sleep(random.uniform(2, 4))

            # 6. 随机移动鼠标（通过JavaScript模拟更自然的移动）
            self.driver.execute_script("""
                function simulateMouseMove() {
                    for(let i = 0; i < 5; i++) {
                        setTimeout(() => {
                            var event = new MouseEvent('mousemove', {
                                'view': window,
                                'bubbles': true,
                                'cancelable': true,
                                'clientX': Math.random() * window.innerWidth,
                                'clientY': Math.random() * window.innerHeight
                            });
                            document.dispatchEvent(event);
                        }, i * 200);
                    }
                }
                simulateMouseMove();
            """)

            self.logger.info("✅ 用户行为模拟完成")

        except Exception as e:
            self.logger.warning(f"⚠️ 模拟用户行为失败: {e}")

    def simulate_mouse_wheel_scroll(self):
        """模拟真实的鼠标滚轮滚动"""
        try:
            self.logger.info("🖱️ 模拟鼠标滚轮滚动...")

            # 使用JavaScript模拟更真实的滚轮事件
            scroll_script = """
                function simulateWheelScroll() {
                    const scrollSteps = [300, -150, 400, -200, 250, -100, 350];
                    let currentStep = 0;

                    function doScroll() {
                        if (currentStep < scrollSteps.length) {
                            const delta = scrollSteps[currentStep];

                            // 创建滚轮事件
                            const wheelEvent = new WheelEvent('wheel', {
                                deltaY: delta,
                                deltaMode: 0,
                                bubbles: true,
                                cancelable: true
                            });

                            // 在随机元素上触发滚轮事件
                            const elements = document.querySelectorAll('body, .content, .main');
                            const randomElement = elements[Math.floor(Math.random() * elements.length)];
                            randomElement.dispatchEvent(wheelEvent);

                            // 同时使用scrollBy确保滚动生效
                            window.scrollBy(0, delta);

                            currentStep++;
                            setTimeout(doScroll, Math.random() * 1000 + 500);
                        }
                    }

                    doScroll();
                }

                simulateWheelScroll();
            """

            self.driver.execute_script(scroll_script)
            time.sleep(random.uniform(5, 8))  # 等待滚动完成

        except Exception as e:
            self.logger.warning(f"⚠️ 滚轮滚动模拟失败: {e}")

    def click_next_page(self):
        """智能点击下一页"""
        try:
            self.logger.info("🔍 正在查找下一页按钮...")

            # 多种下一页按钮的选择器
            next_page_selectors = [
                # 链家常见的下一页选择器
                '.house-lst-page-box .next',
                '.page-box .next',
                'a[title="下一页"]',
                'a.next',
                '.pagination .next',
                '.pagination a[aria-label="下一页"]',
            ]

            for selector in next_page_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if next_button and next_button.is_enabled() and next_button.is_displayed():
                        self.logger.info(f"✅ 找到下一页按钮: {selector}")

                        # 滚动到按钮可见区域
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                        time.sleep(1)

                        # 模拟鼠标悬停
                        actions = ActionChains(self.driver)
                        actions.move_to_element(next_button).perform()
                        time.sleep(random.uniform(0.5, 1.5))

                        # 点击按钮
                        next_button.click()
                        self.logger.info("🖱️ 成功点击下一页按钮")

                        # 等待页面加载
                        time.sleep(random.uniform(3, 6))

                        # 检查是否成功跳转
                        WebDriverWait(self.driver, 10).until(
                            lambda driver: driver.execute_script("return document.readyState") == "complete"
                        )

                        return True

                except Exception as e:
                    continue

            # 如果没找到标准的下一页按钮，尝试查找页码
            self.logger.info("🔍 未找到下一页按钮，尝试查找页码...")
            return self.click_page_number()

        except Exception as e:
            self.logger.error(f"❌ 点击下一页失败: {e}")
            return False

    def click_page_number(self):
        """点击具体页码"""
        try:
            # 获取当前页码
            current_page = self.get_current_page_number()
            if current_page:
                next_page = current_page + 1
                self.logger.info(f"🔍 尝试点击页码 {next_page}...")

                # 查找页码链接
                page_selectors = [
                    f'a[href*="pg{next_page}"]',
                    f'//a[text()="{next_page}"]',
                    f'//a[contains(@href, "pg{next_page}")]',
                ]

                for selector in page_selectors:
                    try:
                        if selector.startswith('//'):
                            page_link = self.driver.find_element(By.XPATH, selector)
                        else:
                            page_link = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if page_link and page_link.is_enabled() and page_link.is_displayed():
                            self.logger.info(f"✅ 找到页码链接: {next_page}")

                            # 滚动到链接可见
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", page_link)
                            time.sleep(1)

                            # 点击页码
                            page_link.click()
                            self.logger.info(f"🖱️ 成功点击页码 {next_page}")

                            time.sleep(random.uniform(3, 6))
                            return True

                    except:
                        continue

            return False

        except Exception as e:
            self.logger.error(f"❌ 点击页码失败: {e}")
            return False

    def get_current_page_number(self):
        """获取当前页码"""
        try:
            # 从URL中提取页码
            current_url = self.driver.current_url
            if 'pg' in current_url:
                import re
                match = re.search(r'pg(\d+)', current_url)
                if match:
                    return int(match.group(1))

            # 从页面元素中查找当前页码
            current_page_selectors = [
                '.house-lst-page-box .cur',
                '.pagination .current',
                '.page-box .cur',
                '.pagination .active',
            ]

            for selector in current_page_selectors:
                try:
                    current_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if current_element:
                        page_text = current_element.text.strip()
                        if page_text.isdigit():
                            return int(page_text)
                except:
                    continue

            return 1  # 默认第一页

        except:
            return 1

    def is_blocked_page(self, html, url):
        """检查是否被阻止"""
        if not html or len(html) < 1000:
            return True

        blocked_indicators = [
            'verifycode', 'captcha', '验证码', '安全验证',
            'antibot', 'robot', '访问异常', '系统检测',
            'cloudflare', 'please wait', '稍后再试'
        ]

        html_lower = html.lower()
        url_lower = url.lower() if url else ""

        for indicator in blocked_indicators:
            if indicator.lower() in html_lower or indicator.lower() in url_lower:
                return True
        return False

    def get_page_with_selenium(self, url):
        """使用Selenium获取页面"""
        if not self.driver:
            if not self.setup_selenium_driver():
                return None, None

        try:
            self.logger.info(f"🌐 访问页面: {url}")

            # 如果已经通过验证且是分页URL，尝试智能导航
            if self.verified_session and 'pg' in url:
                page_num = int(url.split('pg')[1].split('/')[0])
                if page_num > 1:
                    self.logger.info("🔄 使用智能导航方式...")

                    # 尝试点击下一页按钮
                    try:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, '.house-lst-page-box .next')
                        if next_button and next_button.is_enabled():
                            self.logger.info("📄 点击下一页按钮...")
                            next_button.click()
                            time.sleep(random.uniform(3, 6))

                            if not self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                                html = self.driver.page_source
                                current_url = self.driver.current_url
                                self.logger.info(f"📄 智能导航成功，获取内容长度: {len(html)}")
                                return html, current_url
                    except:
                        self.logger.info("📄 下一页按钮不可用，尝试其他方式...")

            # 尝试不同的访问策略
            success = False

            # 策略1: 直接访问
            try:
                self.driver.get(url)
                time.sleep(random.uniform(8, 15))

                if not self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                    success = True
                else:
                    self.logger.warning("🚫 直接访问被阻止，尝试其他策略...")
            except:
                pass

            # 策略2: 通过入口点访问
            if not success:
                success = self.try_different_entry_points()

            # 策略3: 处理验证码
            if not success and self.is_blocked_page(self.driver.page_source, self.driver.current_url):
                if "verifycode" in self.driver.current_url.lower() or "captcha" in self.driver.page_source.lower():
                    success = self.handle_captcha_page()

            if success:
                # 模拟用户浏览行为
                self.simulate_real_user_behavior()

                # 额外的滚轮滚动模拟
                if random.choice([True, False]):  # 50%概率进行额外滚轮滚动
                    self.simulate_mouse_wheel_scroll()

                html = self.driver.page_source
                current_url = self.driver.current_url

                self.logger.info(f"📄 获取内容长度: {len(html)}")
                return html, current_url
            else:
                return None, None

        except Exception as e:
            self.logger.error(f"❌ Selenium获取页面失败: {e}")
            return None, None

    def parse_house_list_lianjia(self, html):
        """解析链家房源列表"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')

        # 调试信息
        title = soup.title.text.strip() if soup.title else "无标题"
        self.logger.info(f"📄 页面标题: {title}")

        # 链家的选择器
        selectors = [
            '.content__list .content__list--item',  # 链家主要选择器
            '.rent-list-wrap .content__list--item',
            '.content__article',
            'div[data-el="rent"]'
        ]

        house_items = []
        for selector in selectors:
            house_items = soup.select(selector)
            if house_items:
                self.logger.info(f"✅ 使用选择器 '{selector}' 找到 {len(house_items)} 个房源")
                break

        if not house_items:
            self.logger.error("❌ 未找到房源条目")
            self.analyze_page_structure(soup)
            self.save_debug_html(html, "lianjia")
            return []

        houses = []
        for idx, item in enumerate(house_items):
            try:
                house_data = self.extract_house_info_lianjia(item, idx + 1)
                if house_data:
                    houses.append(house_data)
                    self.logger.info(f"✅ 解析房源 {idx + 1}: {house_data.get('title', 'N/A')[:30]}...")

            except Exception as e:
                self.logger.error(f"❌ 解析第{idx + 1}个房源失败: {e}")
                continue

        return houses

    def extract_house_info_lianjia(self, item, index):
        """提取链家房源信息"""
        house_data = {}

        try:
            # 标题和链接
            title_elem = item.select_one('.content__list--item--title a')
            if not title_elem:
                title_elem = item.select_one('a[title]')

            if not title_elem:
                return None

            house_data['title'] = title_elem.get_text(strip=True)

            # 链接
            link = title_elem.get('href', '')
            if link and not link.startswith('http'):
                link = 'https://tj.lianjia.com' + link
            house_data['link'] = link

            # 房屋信息
            house_info_elem = item.select_one('.content__list--item--des')
            house_data['house_info'] = house_info_elem.get_text(strip=True) if house_info_elem else ""

            # 位置
            location_elem = item.select_one('.content__list--item--des a')
            house_data['location'] = location_elem.get_text(strip=True) if location_elem else ""

            # 价格
            price_elem = item.select_one('.content__list--item-price em')
            if price_elem:
                price = price_elem.get_text(strip=True)
                house_data['price'] = f"{price}元/月" if price else "价格面议"
            else:
                house_data['price'] = "价格面议"

            # 标签
            tag_elems = item.select('.content__list--item--bottom__tag')
            tags = [tag.get_text(strip=True) for tag in tag_elems]
            house_data['tags'] = ', '.join(tags)

            return house_data

        except Exception as e:
            self.logger.error(f"❌ 提取房源信息出错: {e}")
            return None

    def analyze_page_structure(self, soup):
        """分析页面结构"""
        self.logger.info("🔍 分析页面结构...")

        # 查找可能的容器
        potential_containers = [
            'div[class*="item"]',
            'div[class*="list"]',
            'div[class*="content"]',
            'li[class*="item"]'
        ]

        for selector in potential_containers:
            elements = soup.select(selector)
            if elements:
                self.logger.info(f"   找到 {len(elements)} 个 '{selector}' 元素")

    def save_debug_html(self, html, prefix="debug"):
        """保存调试HTML"""
        try:
            debug_dir = "debug"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = int(time.time())
            filename = f"{prefix}_{timestamp}.html"
            filepath = os.path.join(debug_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html)
            self.logger.info(f"🐛 调试HTML已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"❌ 保存调试HTML失败: {e}")

    def scrape_houses_manual_assist(self, max_pages=3):
        """人工配合爬取房源信息"""
        self.logger.info(f"🤝 开始人工配合爬取模式，最多 {max_pages} 页")
        print("=" * 60)
        print("🤝 人工配合模式说明：")
        print("1. 程序会打开浏览器到第一页")
        print("2. 请您手动完成验证码（如果有）")
        print("3. 程序自动抓取当前页面数据")
        print("4. 请您手动点击下一页")
        print("5. 重复步骤3-4直到完成")
        print("=" * 60)

        successful_pages = 0

        # 初始化浏览器并访问第一页
        if not self.setup_selenium_driver():
            return []

        try:
            # 访问第一页
            self.logger.info("🌐 正在访问第一页...")
            self.driver.get(self.base_url)
            time.sleep(5)

            for page_num in range(1, max_pages + 1):
                self.logger.info(f"\n📄 准备处理第 {page_num}/{max_pages} 页")
                print("-" * 50)

                if page_num == 1:
                    # 第一页：等待用户处理验证码
                    input("👆 请检查页面是否正常加载，如有验证码请完成验证，然后按回车继续...")
                else:
                    # 后续页面：等待用户手动导航
                    input(f"👆 请手动点击进入第 {page_num} 页，然后按回车继续...")

                # 等待页面稳定
                time.sleep(3)

                # 检查当前页面
                current_url = self.driver.current_url
                self.logger.info(f"📍 当前页面: {current_url}")

                # 模拟用户行为
                self.simulate_real_user_behavior()

                # 获取页面内容并解析
                html = self.driver.page_source
                if html:
                    houses = self.parse_house_list_lianjia(html)

                    if houses:
                        self.data.extend(houses)
                        successful_pages += 1
                        self.logger.info(f"✅ 第 {page_num} 页成功解析 {len(houses)} 条房源")
                        self.logger.info(f"📊 累计获取 {len(self.data)} 条房源信息")

                        # 显示前3条数据作为确认
                        print("\n📋 本页前3条房源预览：")
                        for i, house in enumerate(houses[:3]):
                            print(f"   {i+1}. {house.get('title', 'N/A')[:40]}... - {house.get('price', 'N/A')}")
                    else:
                        self.logger.error(f"❌ 第 {page_num} 页未解析到房源信息")
                        print("⚠️ 可能页面结构有变化或加载不完整")

                        # 询问是否继续
                        continue_choice = input("是否继续下一页？(y/n): ").lower()
                        if continue_choice != 'y':
                            break
                else:
                    self.logger.error(f"❌ 第 {page_num} 页获取失败")
                    break

                # 页面间暂停
                if page_num < max_pages:
                    print(f"\n⏳ 准备处理下一页...")
                    time.sleep(2)

        except KeyboardInterrupt:
            self.logger.info("⏹️ 用户中断操作")
        except Exception as e:
            self.logger.error(f"❌ 爬取过程出错: {e}")

        # 结果统计
        self.logger.info(f"\n🎉 人工配合爬取完成！")
        self.logger.info(f"📊 成功页面: {successful_pages}/{max_pages}")
        self.logger.info(f"📊 总获取房源: {len(self.data)} 条")

        return self.data

    def scrape_houses_smart_navigation(self, max_pages=3):
        """智能导航爬取房源信息"""
        self.logger.info(f"🏠 开始智能导航爬取链家房源信息，最多 {max_pages} 页")
        print("=" * 60)

        successful_pages = 0
        failed_pages = []

        # 初始化浏览器并访问第一页
        if not self.setup_selenium_driver():
            return []

        try:
            # 访问第一页
            self.logger.info("🌐 正在访问第一页...")
            html, final_url = self.get_page_with_selenium(self.base_url)

            if not html:
                self.logger.error("❌ 第一页访问失败")
                return []

            for page_num in range(1, max_pages + 1):
                self.logger.info(f"\n📄 正在处理第 {page_num}/{max_pages} 页")
                print("-" * 50)

                if page_num > 1:
                    # 从第二页开始，使用智能点击下一页
                    self.logger.info("🔄 使用智能导航到下一页...")
                    if not self.click_next_page():
                        self.logger.error(f"❌ 无法导航到第 {page_num} 页")
                        failed_pages.append(page_num)
                        self.failed_attempts += 1

                        # 如果智能导航失败，尝试直接访问URL
                        self.logger.info("🔄 尝试直接访问URL...")
                        url = f"{self.base_url}pg{page_num}/"
                        html, final_url = self.get_page_with_selenium(url)
                        if not html:
                            continue
                    else:
                        # 智能导航成功，获取当前页面内容
                        time.sleep(random.uniform(2, 4))
                        html = self.driver.page_source

                # 解析房源信息
                if html:
                    houses = self.parse_house_list_lianjia(html)

                    if houses:
                        self.data.extend(houses)
                        successful_pages += 1
                        self.logger.info(f"✅ 第 {page_num} 页成功解析 {len(houses)} 条房源")
                        self.logger.info(f"📊 累计获取 {len(self.data)} 条房源信息")
                        self.failed_attempts = 0

                        # 标记验证成功
                        if not self.verified_session:
                            self.verified_session = True
                            self.logger.info("✅ 验证状态已确认")
                    else:
                        self.logger.error(f"❌ 第 {page_num} 页未获取到房源信息")
                        failed_pages.append(page_num)
                        self.failed_attempts += 1
                else:
                    self.logger.error(f"❌ 第 {page_num} 页获取失败")
                    failed_pages.append(page_num)
                    self.failed_attempts += 1

                # 如果连续失败，提前退出
                if self.failed_attempts >= 2:
                    self.logger.error(f"🛑 连续失败 {self.failed_attempts} 次，停止爬取")
                    break

                # 页面间延迟 - 智能延迟策略
                if page_num < max_pages:
                    if self.verified_session and successful_pages > 0:
                        # 已验证且成功获取过数据，使用较短延迟
                        delay = random.uniform(5, 12)
                        self.logger.info(f"⏳ 智能延迟 {delay:.1f} 秒...")
                    else:
                        # 未验证或失败情况，使用较长延迟
                        delay = random.uniform(15, 25)
                        self.logger.info(f"⏳ 安全延迟 {delay:.1f} 秒...")
                    time.sleep(delay)

        except Exception as e:
            self.logger.error(f"❌ 爬取过程出错: {e}")

        # 结果统计
        self.logger.info(f"\n🎉 智能导航爬取完成！")
        self.logger.info(f"📊 成功页面: {successful_pages}/{max_pages}")
        self.logger.info(f"📊 总获取房源: {len(self.data)} 条")

        return self.data

    def scrape_houses(self, max_pages=3):
        """爬取房源信息"""
        self.logger.info(f"🏠 开始爬取链家房源信息，最多 {max_pages} 页")
        print("=" * 60)

        successful_pages = 0
        failed_pages = []

        for page_num in range(1, max_pages + 1):
            self.logger.info(f"\n📄 正在处理第 {page_num}/{max_pages} 页")
            print("-" * 50)

            # 构建URL
            if page_num == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}pg{page_num}/"

            # 获取页面内容
            html, final_url = self.get_page_with_selenium(url)

            if html:
                # 解析房源信息
                houses = self.parse_house_list_lianjia(html)

                if houses:
                    self.data.extend(houses)
                    successful_pages += 1
                    self.logger.info(f"✅ 第 {page_num} 页成功解析 {len(houses)} 条房源")
                    self.logger.info(f"📊 累计获取 {len(self.data)} 条房源信息")
                    self.failed_attempts = 0
                else:
                    self.logger.error(f"❌ 第 {page_num} 页未获取到房源信息")
                    failed_pages.append(page_num)
                    self.failed_attempts += 1
            else:
                self.logger.error(f"❌ 第 {page_num} 页获取失败")
                failed_pages.append(page_num)
                self.failed_attempts += 1

            # 如果连续失败，提前退出
            if self.failed_attempts >= 2:
                self.logger.error(f"🛑 连续失败 {self.failed_attempts} 次，停止爬取")
                break

            # 页面间延迟 - 智能延迟策略
            if page_num < max_pages:
                if self.verified_session and successful_pages > 0:
                    # 已验证且成功获取过数据，使用较短延迟
                    delay = random.uniform(10, 20)
                    self.logger.info(f"⏳ 智能延迟 {delay:.1f} 秒...")
                else:
                    # 未验证或失败情况，使用较长延迟
                    delay = random.uniform(25, 45)
                    self.logger.info(f"⏳ 安全延迟 {delay:.1f} 秒...")
                time.sleep(delay)

        # 结果统计
        self.logger.info(f"\n🎉 爬取完成！")
        self.logger.info(f"📊 成功页面: {successful_pages}/{max_pages}")
        self.logger.info(f"📊 总获取房源: {len(self.data)} 条")

        return self.data

    def save_data(self, filename_prefix="tianjin_houses_lianjia"):
        """保存数据"""
        if not self.data:
            self.logger.warning("⚠️ 没有数据可保存")
            return False

        timestamp = time.strftime("%Y%m%d_%H%M%S")

        # 保存CSV
        csv_filename = f"{filename_prefix}_{timestamp}.csv"
        try:
            df = pd.DataFrame(self.data)
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"💾 数据已保存到: {csv_filename}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 保存数据失败: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("✅ WebDriver 已关闭")
            except Exception as e:
                self.logger.error(f"❌ 关闭WebDriver失败: {e}")


def main():
    """主函数"""
    print("🏠 防验证码房源爬虫 - 链家版")
    print("=" * 60)
    print("请选择爬取模式：")
    print("1. 🤖 自动模式 - 程序自动处理所有页面（可能遇到验证码问题）")
    print("2. � 智能导航模式 - 自动点击下一页按钮（推荐）")
    print("3. �🤝 人工配合模式 - 您手动导航，程序负责抓取")
    print("=" * 60)

    while True:
        choice = input("请输入选择 (1, 2 或 3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("❌ 请输入 1, 2 或 3")

    scraper = AntiCaptchaHousingScraper()

    try:
        if choice == '1':
            print("\n🤖 启动自动模式...")
            print("💡 如果遇到验证码，程序会等待您手动完成验证")
            houses = scraper.scrape_houses(max_pages=50)
        else:
            print("\n🤝 启动人工配合模式...")
            max_pages = input("请输入要爬取的页数 (默认10页): ").strip()
            try:
                max_pages = int(max_pages) if max_pages else 10
            except:
                max_pages = 10
            houses = scraper.scrape_houses_manual_assist(max_pages=max_pages)

        if houses:
            scraper.save_data()
            print(f"\n✅ 成功获取 {len(houses)} 条房源信息！")

            # 显示统计信息
            if len(houses) > 0:
                print("\n📊 数据统计：")
                print(f"   总房源数量: {len(houses)}")

                # 价格统计
                prices = []
                for house in houses:
                    price_str = house.get('price', '')
                    if '元/月' in price_str:
                        try:
                            price = int(''.join(filter(str.isdigit, price_str)))
                            if 100 <= price <= 50000:
                                prices.append(price)
                        except:
                            pass

                if prices:
                    print(f"   平均租金: {sum(prices)//len(prices)} 元/月")
                    print(f"   价格范围: {min(prices)} - {max(prices)} 元/月")
        else:
            print("❌ 未获取到任何房源数据")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        if scraper.data:
            print("💾 正在保存已获取的数据...")
            scraper.save_data()
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        if scraper.data:
            print("💾 正在保存已获取的数据...")
            scraper.save_data()
    finally:
        scraper.cleanup()

    print("\n🎉 程序执行完毕！")


if __name__ == "__main__":
    main()