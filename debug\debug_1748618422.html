<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en"><head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <meta http-equiv="Cache-Control" content="no-transform">
    <title>404-安居客</title>
    <meta name="baidu-site-verification" content="e8abd676df9f995bc969ac138b1c0f4d">
    <meta name="sogou_site_verification" content="7rtgKfBjbl">
    <meta name="360-site-verification" content="f7b8b308108b2c1c2de2825948822256">
    <meta name="google-site-verification" content="drkSj5A3WGSgkMXwzh6UfezwLEMsEXoQlMHL25oE1kA">
    <meta baidu-gxt-verify-token="9e7961d9a5d01603e5c2ae9bccffb9c2">
    <meta name="shenma-site-verification" content="da9c53da88979ec98afae25b1ca3e43b">
    <link rel="stylesheet" rev="stylesheet" href="https://pages.anjukestatic.com/frs/fangfe/anjuke-zufang-pc-seo/1.0/school/index_v20191220200442.css" type="text/css">
    <link rel="shortcut icon" href="https://pages.anjukestatic.com/usersite/site/img/global/1/favicon.ico" type="image/ico">
    <link rel="icon" href="https://pages.anjukestatic.com/usersite/site/img/global/1/favicon.ico" type="image/ico">
    <script src="//j1.58cdn.com.cn/frs/libs/promise-polyfill/1.0/polyfill.min.js"></script>
    <!-- 熊掌号所需参数 -->
</head>
<body>
<div class="navhead">
    <div class="header-center clearfix">
        <!-- LOGO -->
        <a href="https://.anjuke.com" class="logo">anjuke</a>

        <!-- Navigation -->
        <div class="cityselect">
            <div id="switch_apf_id_5" class="city-view">
                上海<i class="iconfont triangle-down"></i>
            </div>
            <div id="city_list" class="city-list" style="display:none;">
                <dl>
                    <dt>华北东北</dt>
                    <dd>
                        <a href="https://beijing.anjuke.com/" title="北京二手房">北京</a>
                        <a href="https://tianjin.anjuke.com/" title="天津二手房">天津</a>
                        <a href="https://dalian.anjuke.com/" title="大连二手房">大连</a>
                        <a href="https://sjz.anjuke.com/" title="石家庄二手房">石家庄</a>
                        <a href="https://heb.anjuke.com/" title="哈尔滨二手房">哈尔滨</a>
                        <a href="https://sy.anjuke.com/" title="沈阳二手房">沈阳</a>
                        <a href="https://ty.anjuke.com/" title="太原二手房">太原</a>
                        <a href="https://cc.anjuke.com/" title="长春二手房">长春</a>
                        <a href="https://weihai.anjuke.com/" title="威海二手房">威海</a>
                        <a href="https://weifang.anjuke.com/" title="潍坊二手房">潍坊</a>
                        <a href="https://huhehaote.anjuke.com/" title="呼和浩特二手房">呼和浩特</a>
                        <a href="https://baotou.anjuke.com/" title="包头二手房">包头</a>
                    </dd>
                </dl>
                <dl>
                    <dt>华东地区</dt>
                    <dd>
                        <a href="https://shanghai.anjuke.com/" title="上海二手房">上海</a>
                        <a href="https://hangzhou.anjuke.com/" title="杭州二手房">杭州</a>
                        <a href="https://suzhou.anjuke.com/" title="苏州二手房">苏州</a>
                        <a href="https://nanjing.anjuke.com/" title="南京二手房">南京</a>
                        <a href="https://wuxi.anjuke.com/" title="无锡二手房">无锡</a>
                        <a href="https://jinan.anjuke.com/" title="济南二手房">济南</a>
                        <a href="https://qd.anjuke.com/" title="青岛二手房">青岛</a>
                        <a href="https://ks.anjuke.com/" title="昆山二手房">昆山</a>
                        <a href="https://nb.anjuke.com/" title="宁波二手房">宁波</a>
                        <a href="https://nc.anjuke.com/" title="南昌二手房">南昌</a>
                        <a href="https://fz.anjuke.com/" title="福州二手房">福州</a>
                        <a href="https://hf.anjuke.com/" title="合肥二手房">合肥</a>
                        <a href="https://xuzhou.anjuke.com/" title="徐州二手房">徐州</a>
                        <a href="https://zibo.anjuke.com/" title="淄博二手房">淄博</a>
                    </dd>
                </dl>
                <dl>
                    <dt>华南地区</dt>
                    <dd>
                        <a href="https://shenzhen.anjuke.com/" title="深圳二手房">深圳</a>
                        <a href="https://guangzhou.anjuke.com/" title="广州二手房">广州</a>
                        <a href="https://foshan.anjuke.com/" title="佛山二手房">佛山</a>
                        <a href="https://cs.anjuke.com/" title="长沙二手房">长沙</a>
                        <a href="https://sanya.anjuke.com/" title="三亚二手房">三亚</a>
                        <a href="https://huizhou.anjuke.com/" title="惠州二手房">惠州</a>
                        <a href="https://dg.anjuke.com/" title="东莞二手房">东莞</a>
                        <a href="https://haikou.anjuke.com/" title="海口二手房">海口</a>
                        <a href="https://zh.anjuke.com/" title="珠海二手房">珠海</a>
                        <a href="https://zs.anjuke.com/" title="中山二手房">中山</a>
                        <a href="https://xm.anjuke.com/" title="厦门二手房">厦门</a>
                        <a href="https://nanning.anjuke.com/" title="南宁二手房">南宁</a>
                        <a href="https://quanzhou.anjuke.com/" title="泉州二手房">泉州</a>
                        <a href="https://liuzhou.anjuke.com/" title="柳州二手房">柳州</a>
                    </dd>
                </dl>
                <dl>
                    <dt>中 西 部</dt>
                    <dd>
                        <a href="https://chengdu.anjuke.com/" title="成都二手房">成都</a>
                        <a href="https://chongqing.anjuke.com/" title="重庆二手房">重庆</a>
                        <a href="https://wuhan.anjuke.com/" title="武汉二手房">武汉</a>
                        <a href="https://zhengzhou.anjuke.com/" title="郑州二手房">郑州</a>
                        <a href="https://xa.anjuke.com/" title="西安二手房">西安</a>
                        <a href="https://km.anjuke.com/" title="昆明二手房">昆明</a>
                        <a href="https://gy.anjuke.com/" title="贵阳二手房">贵阳</a>
                        <a href="https://lanzhou.anjuke.com/" title="兰州二手房">兰州</a>
                        <a href="https://luoyang.anjuke.com/" title="洛阳二手房">洛阳</a>
                    </dd>
                </dl>
                <div class="morecity"><a href="https://www.anjuke.com/sy-city.html">更多城市&gt;</a></div>
            </div>
        </div>
        <ul class="navigation" id="navigation">
            <li><a href="http://shanghai.anjuke.com/" _soj="navigation">首 页</a>
            </li>
            <li><a href="http://sh.fang.anjuke.com/" _soj="navigation">新 房</a>
                <ul>
                    <li class="nav-triangle"><span></span></li>
                    <li><a href="http://sh.fang.anjuke.com/loupan/" _soj="navigation">新盘</a></li>
                    <li><a href="http://sh.news.anjuke.com/" _soj="navigation">楼讯</a></li>
                    <li><a href="http://sh.fang.anjuke.com/hui/" _soj="navigation">热门活动</a></li>
                    <li><a href="http://sh.fang.anjuke.com/kft/zhuanche/" _soj="navigation">看房团</a></li>
                    <li><a href="http://sh.fang.anjuke.com/fangyuan/" _soj="navigation">房源</a></li>
                    <li><a href="http://sh.fang.anjuke.com/commercial/" _soj="navigation">商业地产</a></li>
                    <li><a href="http://haiwai.anjuke.com" _soj="navigation">海外地产</a></li>
                    <li><a href="http://sh.fang.anjuke.com/brand/list/" _soj="navigation">品牌专区</a></li>
                </ul>
            </li>
            <li><a href="http://shanghai.anjuke.com/sale/" _soj="navigation">二手房</a>
                <ul>
                    <li class="nav-triangle"><span></span></li>
                    <li><a href="http://shanghai.anjuke.com/sale/" _soj="navigation">房源</a></li>
                    <li><a href="http://shanghai.anjuke.com/community/" _soj="navigation">小区</a></li>
                    <li class="hr"></li>
                    <li><a href="http://shanghai.anjuke.com/school/" _soj="navigation">学校</a></li>
                    <li><a href="http://shanghai.anjuke.com/map/sale/" _soj="navigation">地图找房</a></li>
                    <li><a href="http://www.anjuke.com/topic/" _soj="navigation">主题找房</a></li>
                    <li><a href="http://shanghai.anjuke.com/tycoon/" _soj="navigation">找经纪人</a></li>
                    <li><a href="http://shanghai.anjuke.com/tycoon/paiming/" _soj="navigation">优秀经纪人榜</a></li>
                    <li><a href="http://shanghai.anjuke.com/tycoon/weiguijingjiren/" _soj="navigation">违规经纪人</a></li>
                    <li><a href="http://www.anjuke.com/mobile?from=navigation" _soj="navigation">我要卖房</a></li>
                </ul>
            </li>
            <li><a href="http://sh.zu.anjuke.com/" _soj="navigation">租 房</a>
                <ul>
                    <li class="nav-triangle"><span></span></li>
                    <li><a href="http://sh.zu.anjuke.com/" _soj="navigation">区域找房</a></li>
                    <li><a href="http://sh.zu.anjuke.com/ditie/" _soj="navigation">地铁找房</a></li>
                    <li class="hr"></li>
                    <li><a href="http://sh.zu.anjuke.com/ditu/" _soj="navigation">地图找房</a></li>
                    <li><a href="http://shanghai.anjuke.com/community/" _soj="navigation">小区</a></li>
                    <li><a href="http://sh.zu.anjuke.com/duanzu/" _soj="navigation">民宿短租</a></li>
                </ul>
            </li>
            <li><a href="http://sh.sydc.anjuke.com/xzl-zu/" _soj="navigation">商铺写字楼</a>
                <ul>
                    <li class="nav-triangle"><span></span></li>
                    <li><a href="http://sh.sydc.anjuke.com/sp-zu/" _soj="navigation">商铺</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/sp-zu/" _soj="navigation">商铺出租</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/sp-shou/" _soj="navigation">商铺出售</a></li>
                    <li class="hr"></li>
                    <li><a href="http://sh.fang.anjuke.com/sp/all/w4/" _soj="navigation">商铺新盘</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/xzl-zu/" _soj="navigation">写字楼</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/xzl-zu/" _soj="navigation">写字楼出租</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/xzl-shou/" _soj="navigation">写字楼出售</a></li>
                    <li><a href="http://sh.fang.anjuke.com/xzl/all/w5/" _soj="navigation">写字楼新盘</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/shangban/loupan/?from=spxzl_index_float_bglp" _soj="navigation">办公楼盘</a></li>
                    <li><a href="http://sh.sydc.anjuke.com/shang-lhbg/?from=spxzl_index_float_lhbg" _soj="navigation">联合办公</a></li>
                </ul>
            </li>
            <li><a href="http://haiwai.anjuke.com" _soj="navigation">海外地产</a>
            </li>
            <li><a href="http://jiazhuang.anjuke.com/shanghai/gongsi/" _soj="navigation">装修</a>
            </li>
            <li><a href="http://sh.news.anjuke.com/" _soj="navigation">楼 讯</a>
            </li>
            <li><a href="http://ai.anjuke.com/?action=hydc" _soj="navigation">房产研究院</a>
            </li>
            <li><a href="http://shanghai.anjuke.com/market/" _soj="navigation">房 价</a>
            </li>
            <li><a href="http://shanghai.anjuke.com/ask/" _soj="navigation">问 答</a>
            </li>
        </ul>

        <!-- UserLogin Pc_Common_HeaderNavComponent-->
        <div class="nav-right">
            <div id="user_box" class="userbox">
                <i class="icon icon-user"></i>
                <a href="https://login.anjuke.com/login/form?history=aHR0cHM6Ly9zaGFuZ2hhaS5hbmp1a2UuY29tL3NpdGVtYXAvemYvbWV0cm8v" rel="nofollow">登录</a>
                <a href="https://user.anjuke.com/register/" rel="nofollow">注册</a>
            </div>

            <a href="https://www.anjuke.com/mobile?from=navigation" rel="nofollow" class="download-app">
                <i class="icon icon-apple"></i>
                <i class="icon icon-android"></i>
                下载APP
            </a>
        </div>
    </div>
</div>

<style>
    /*  */
    @charset "utf-8";
    body, h1, h2, h3, h4, h5, h6, dl, dt, dd, ul, ol, li, th, td, p, form ,button, input, textarea, fieldset, blockquote, img, iframe, pre, code, hr, legend{padding:0;margin:0;}
    body {color:#333;font:12px Arial, Helvetica, sans-serif, Simsun;}
    h1, h2, h3, h4, h5, h6 {font-size:100%; font-weight:normal;}
    ol, ul {list-style:none;}
    img {border:none;vertical-align:top;}
    input, select, input, textarea {font-size:12px;outline:none;}
    select, input {vertical-align:middle;}
    input[type="reset"]::-moz-focus-inner,
    input[type="button"]::-moz-focus-inner,
    input[type="submit"]::-moz-focus-inner,
    input[type="file"] > input[type="button"]::-moz-focus-inner {border:none;padding:0; margin-bottom: }
    textarea {resize:none;}
    fieldset {border:none;}
    table {border-collapse:collapse;border-spacing:0;}
    caption, th {text-align:left;}
    address, cite, code, em, th, i, strong {font-style:normal;font-weight:normal;}
    a{text-decoration:none;outline:none;}
    a:hover{text-decoration:none;}

    body{
        font-family: "Microsoft YaHei", "微软雅黑","Hiragino Sans GB", tahoma, arial, simhei;
        color: #333;
    }
    .none {
        display: none;
    }
    a{
        color: #666;
    }
    a:hover{
        color: #666;
    }

    .clearfix:after{
        content: '.';
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
    }
    .triangle-down{
        width:0;
        height:0;
        overflow: hidden;
        display: inline-block;
        border-left:4px solid #fff;
        border-right:4px solid #fff;
        border-top:4px solid #999;
        border-bottom:4px solid #fff;
    }

    @font-face {
        font-family: 'iconfont';
        src: url("//pages.anjukestatic.com/usersite/site/img/user/font20191031-01/iconfont.eot"); /* IE9*/
        src: url("//pages.anjukestatic.com/usersite/site/img/user/font20191031-01/iconfont.eot?#iefix") format('embedded-opentype'),
        url("//pages.anjukestatic.com/usersite/site/img/user/font20191031-01/iconfont.woff") format('woff'),
        url("//pages.anjukestatic.com/usersite/site/img/user/font20191031-01/iconfont.ttf") format('truetype'),
        url("//pages.anjukestatic.com/usersite/site/img/user/font20191031-01/iconfont.svg#iconfont") format('svg'); /* iOS 4.1- */
    }
    .city-view .triangle-down{
        margin-left:5px;
        border-top-color: #a7a7a7;
    }
    .city-list{
        position: absolute;
        z-index: 99;
        left:0;
        top:22px;
        font-size: 12px;
        border:1px solid #d8d8d8;
        background: #f9f9f9;
        width:350px;
        padding:7px 0 0;
        border-radius: 0 0 4px 4px;
    }
    .city-list dl{
        margin:0 13px;
        padding:7px 0 6px;
        line-height: 23px;
    }
    .city-list dl~dl{
        border-top: 1px solid #ccc;
    }
    .city-list dt{
        float: left;
        width:72px;
        color: #666;
    }
    .city-list dd{
        zoom: 1;
        overflow: hidden;
        margin-left: 72px;
    }
    .city-list a{
        color:#1a519f;
        padding-right: 11px;
        white-space: nowrap;
    }
    .city-list dd a{
        float: left;
    }
    .city-list dd a:hover{
        color: #f60;
        text-decoration: underline;
    }
    .morecity{
        height:25px;
        text-align: right;
    }

    .auto-grayback{
        background-color: #eee;
        cursor: pointer;
    }
    .auto-ul{
        padding:0px;
        margin: 0px;
    }
    .auto-ul li{
        position: relative;
        padding: 0 10px;
        height: 30px;
        line-height: 30px;
    }
    .auto-ul li b{
        font-weight: normal;
        position: absolute;
        right: 5px;
        top: 0;
    }

    :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
        color: #999; opacity:1;
    }

    ::-moz-placeholder { /* Mozilla Firefox 19+ */
        color: #999;opacity:1;
    }

    input:-ms-input-placeholder{
        color: #999;opacity:1;
    }

    input::-webkit-input-placeholder{
        color: #999;opacity:1;
    }
    /**
    **/
    /* 顶部导航 */
    .topbar{
        font-size: 14px;
        height:44px;
        border-bottom: 1px solid #ddd;
    }
    .topbar a{
        color: #666;
    }
    .topbar-qa a {
        color: #5f5f5f;
    }
    .topbar a:hover{
        color: #f60;
    }
    .nav {
        position: relative;
    }
    .nav-site{
        float: left;
        padding-top:17px;
        line-height: 14px;
    }
    .nav-site li{
        float: left;
        padding:0 10px;
        border-left:1px solid #ddd;
    }
    .nav-site li.first{
        padding-left:0;
        border-left:none;
    }
    .user{
        float: right;
        text-align: right;
        padding-top:12px;
        margin-right: -10px;
    }
    .custom-link{
        display: inline-block;
        *display: inline;
    }
    .userblock{
        display: inline-block;
        *display: inline;
        position: relative;
        height:16px;
        padding:2px 10px;
        font-size: 13px;
        /*border-left:1px solid #ddd;*/
        zoom:1;
    }
    #user_box .userblockfirst{
        padding-top: 14px;
    }

    .userblockfirst{
        border-left:none;
    }

    .userblock .link{
        position: relative;
        display: inline-block;
        line-height: 16px;
        margin-right:5px;
        *zoom:1;
        outline: none;
    }

    .userblock .usermsg{
        padding-right: 35px;
    }

    .userblock .link .icon-point{
        position: absolute;
        right:0;
        top:-9px;
        text-align: center;
        line-height: 32px;
        color:#fff;
        margin-top:0px;
    }

    .userblock span{
        display: inline-block;
        line-height: 14px;
        margin-right:5px;
    }

    .userblock ul{
        position: absolute;
        display: none;
        z-index: 1000;
        right:-4px;
        top:33px;
        width:115px;
        border: 1px solid #D6D6D6;
        background-color: #F7F7F7;
        border-radius: 0 0 4px 4px;
        padding: 10px;
        border-top: 0;
        text-align: left;
    }

    .userblock ul li{
        padding:5px 0 10px 8px;
    }

    .userblock ul li.hr{
        height:0;
        border-top: 1px solid #ddd;
        margin: 3px 0 5px;
        padding: 0;
    }

    .userblock ul a{
        color:#666;
        margin:;
    }
    .userblock ul a:hover{
        color: #f60;
    }
    .userblock ul em{
        color:#f60;
        margin-left: 5px;
    }

    .userblock .triangle-down{
        margin:2px 0 0 5px;
    }

    .userlogin .icon-people{
        vertical-align: top;
        margin:0px 4px 0 0;
        width: 18px;
        height: 18px;
    }

    .userbox ul {
        left: -32px !important;
    }

    .userfavorite .icon-love{
        vertical-align: top;
        margin:0px 4px 0 0;
    }

    .userfavorite .link{
        margin-right: 0;
    }

    .header{
        height:62px;
    }

    .logo{
        display: block;
        overflow: hidden;
        float: left;
        margin: 18px 15px 0 0;
        text-indent: -999em;
        width: 85px;
        height: 25px;
        background: url("//pages.anjukestatic.com/usersite/site/img/global/2/logo.svg") 0 0 no-repeat;
        background: url("//pages.anjukestatic.com/usersite/site/img/global/2/logox32.png") 0 0 no-repeat\0;
        *background: url("//pages.anjukestatic.com/usersite/site/img/global/2/logox8.png") 0 0 no-repeat;
        background-size: 85px 25px;
    }
    .logo-site{
        float: left;
        margin-top:18px;
        border-left: 1px solid #DDD;
        padding: 0 10px 0 15px;
        color: #62ab00;
        font-size: 24px;
        height: 25px;
        line-height: 25px;
    }
    .logo-site:hover{
        color: #62ab00;
        text-decoration: none;
    }
    .cityselect{
        float: left;
        margin:25px 0 0 10px;
    }

    .nav{
        background: #62ab00;
        height:40px;
        color:#fff;
    }
    .nav a{
        color: #fff;
    }
    .nav li{
        float: left;
        position: relative;
    }
    .nav li.fr{
        float: right;
    }
    .nav li a{
        display: inline-block;
        padding:0 25px;
        height:40px;
        font-size: 16px;
        line-height: 40px;
    }
    .nav li a:hover{
        background: #4A8101;
    }
    .nav li a.current, .nav li a.current:hover{
        background: #599700;
    }
    .search-form{
        float: right;
        margin-top:15px;
        _margin-top: -32px;
    }
    .hot{
        z-index: 1;
    }

    /* 问答 */
    .topbar-qa {
        background: #fff;
    }
    .nav .appdown:link, .sec-bar .appdown:visited {
        position: absolute;
        top: 6px;
        right: 30px;
        /* margin: 6px 30px 0 0; */
        width: 110px;
        height: 27px;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        background: #dce8cc;
        cursor: pointer;
        color: #666;
        padding: 0;
        font-size: 14px;
        line-height: 27px;
        text-align: center;
        overflow: hidden;
    }

    .nav .appdown .iconfont {
        color: #62ab00;
        font-size: 18px;
        margin-top: -2px;
    }
    .header-center .appdown:link,
    .header-center .appdown:visited {
        position: absolute;
        top: 6px;
        right: 30px;
        /*margin: 6px 30px 0 0;*/
        width: 110px;
        height: 27px;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        background: #dce8cc;
        cursor: pointer;
        color: #666;
        padding: 0;
        font-size: 14px;
        line-height: 27px;
        text-align: center;
        overflow: hidden;
    }
    .header-center .appdown i {
        display: inline-block;
        font-style: normal;
        *zoom: 1;
    }
    .header-center .appdown .iconfont {
        color: #62ab00;
        font-size: 18px;
        vertical-align: middle;
        margin-top: -2px;
    }
    #toRent:hover .personal-rent-dialog{
        display: block;
    }
    .personal-rent-dialog {
        z-index: 100;
        position: absolute;
        display: none;
        left: -42px;
        top: 62px;
        text-align: center;
        width: 198px;
        padding: 10px 0 20px;
        border: 1px solid #e6e6e6;
        background: #fff;
        border-radius: 4px;
    }
    .personal-rent-dialog .arrow_box {
        position: absolute;
        top: -1px;
        left: 100px;
        background: #fff;
        border: 1px solid #e6e6e6;
    }
    .personal-rent-dialog .arrow_box i, .personal-rent-dialog .arrow_box em {
        bottom: 100%;
        left: 50%;
        border: solid transparent;
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
    }
    .personal-rent-dialog .arrow_box i {
        border-color: rgba(230,230,230,0);
        border-bottom-color: #e6e6e6;
        border-width: 11px;
        margin-left: -11px;
    }
    .personal-rent-dialog .arrow_box em {
        border-color: rgba(255,255,255,0);
        border-bottom-color: #fff;
        border-width: 10px;
        margin-left: -10px;
    }
    .personal-rent-dialog .link {
        display: block;
        margin: 0 auto;
        width: 100%;
    }
    .personal-rent-dialog .tip {
        font-size: 12px;
        line-height: 17px;
        color: #666;
        font-weight: normal;
    }







    .footer{
        clear: both;
        border-top: 1px solid #e6e6e6;
        padding: 15px 0 30px;
        font-size: 12px;
        color: #333;
    }
    .footer a {
        display: inline-block;
        font-size: 12px;
        color: #333;
    }
    .footer a:hover{
        color: #f60;
        text-decoration: underline;
    }
    .footer .spe-lines {
        display: inline-block;
        width: 1px;
        height: 10px;
        background: #ccc;
        margin: 0 10px;
        font-size: 0;
        text-indent: -9999px;
        overflow: hidden;
        vertical-align: -1px;
    }
    .footer .links{
        padding-bottom: 5px;
        font-size: 0;
        text-align: center;
    }
    .footer .links li{
        display: inline-block;
        *display: inline;
        *zoom: 1;
        height: 20px;
        line-height: 20px;
    }


    .footer .show-mod {
        display: none;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        width: 730px;
        padding: 3px 10px;
        margin: 0 auto 5px;
        overflow: hidden;
    }
    .footer .show-mod ul {
        *zoom:1;
    }
    .footer .show-mod ul:after {
        clear: both;
        content: ".";
        display: block;
        font-size: 0;
        height: 0;
        line-height: 0;
        visibility: hidden;
    }
    .footer .show-mod li {
        float: left;
        _display: inline;
        margin-right: 22px;
        line-height: 24px;
    }

    .footer .other-city ul{
        width: 736px;
    }
    .footer .other-city li {
        width: 76px;
        height: 24px;
        line-height: 24px;
        margin-right: 16px;
        white-space:normal;
        word-break:break-all;
        word-wrap:break-word;
        overflow: hidden;
    }
    .footer .cp-mod{
        text-align: center;
        line-height: 20px;
    }
    .footer .gov-mod {
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin-top: 8px;
    }
    .footer .gov-mod a,
    .footer .gov-mod a:hover {
        color: #666;
        text-decoration: none;
    }
    .footer .gov-mod img {
        display: inline-block;
        vertical-align: -5px;
    }
    .footer .license{
        text-align: center;
        padding: 20px 0 0;
    }
    .footer .license a{
        margin:0 10px;
    }
    .footer .cnnic{
        position: relative;
        bottom: 6px;
        margin-left: 10px;
    }
    .footer .jubao-img {
        width: 125px;
        height: 60px;
    }
    .footer .jubao-img img {
        width: 100%;
        height: 100%;
        border: solid 1px #ccc;
    }
    .footer .wangxin-img {
        width: 110px;
        height: 75px;
        vertical-align: -8px;
    }
    .footer .wangxin-img img {
        width: 100%;
        height: 100%;
        border: solid 1px #ccc;
    }/**
    */
    .sidebar {
        display: none;
        position: fixed;
        top: 0;
        right: 0;
        width: 38px;
        height: 100%;
        background-color: #f7f7f7;
        box-shadow: 0px 0px 2px #999;
        z-index: 1000;
        font-family: "Hiragino Sans GB","Microsoft YaHei","微软雅黑",tahoma,Arial,simhei;
    }
    .sidebar-nav {
        position: absolute;
        right: 0;
        top:50%;
        margin-top: -98px;
    }
    .sidebar-top {
        position: fixed;
        display: none;
        bottom: 30px;
        right: 0;
        z-index: 1000;
    }
    .sidebar-mod .sid-nav {
        position: relative;
        display: block;
        width: 38px;
        height: 40px;
        margin-bottom: 12px;
        text-align: center;
        color: #333;
        font-size:12px;
    }
    .sidebar-mod a:hover,
    .sd-top-sig {
        background-color: #62ab00;
        color: #fff;
        text-decoration: none;
    }
    .sidebar-mod .iconfont {
        height: 20px;
        line-height: 20px;
        font-size: 20px;
        margin-top: 2px;
    }
    .sidebar-mod .iconfont22 {
        font-size: 20px;
        margin-top: 2px;
    }
    .sidebar-top .iconfont {
        margin-top: 10px;
    }
    .sidebar-mod a:hover .iconfont ,
    .sd-top-sig .iconfont{
        color: #fff;
    }
    .sidebar p {
        height: 13px;
        line-height: 13px;
        margin-top: 4px;
    }
    .sidebar-nav-hover {
        position: absolute;
        right: 38px;
        top: 0;
        width: 0;
        height: 40px;
        line-height: 40px;
        background-color: #666;
        text-align: center;
        color: #fff;
        font-size: 14px;
        overflow: hidden;
    }
    .nav-arrow {
        content: '';
        width: 0;
        height: 0;
        overflow: hidden;
        border-left: 4px solid #666;
        border-right: 4px solid transparent;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        position: absolute;
        top: 16px;
        left: 0;
        display: none;
    }
    .sidebar-mod a:hover .nav-arrow{
        display: block;
    }
    .nav-sao{
        border-left: 4px solid #fff;
    }
    .sidebar-sao{
        top: -55px;
        width: 0;
        height: 36px;
        padding-top: 114px;
        background-image: url('//pages.anjukestatic.com/usersite/site/img/global/2/pc-sidebar.png ');
        background-size: cover;
        filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(
                src='//pages.anjukestatic.com/usersite/site/img/global/2/pc-sidebar.png ',
                sizingMethod='scale');
        color: #333;
    }
    .sidebar-top a.sid-nav{
        margin-bottom: 0;
    }
    /**
    **/
    .navhead{
        position: relative;
        z-index: 9998;
        background: #62AB00;
        height:40px;
        width:100%;
        font-family: "Microsoft YaHei", "微软雅黑","Hiragino Sans GB", tahoma, arial, simhei;
    }

    .navhead .logo{
        width: 90px;
        height: 30px;
        float: left;
        margin: 4px 0 0;
        background: url(//pages.anjukestatic.com/usersite/site/img/global/2/header/CommLogoV3.png) no-repeat;
    }
    .cityselect{
        float: left;
        _width: 70px;
        margin:15px 3px 0 1px;
        font-size: 12px;
        color:#fff;
    }
    .cityselect .triangle-down{
        border-color:#62AB00;
        border-top: 4px solid #fff;
        position: relative;
        top:2px;
    }
    .city-view{
        padding-right:0px;
    }
    .navigation{
        float: left;
    }
    .navigation li{
        position: relative;
        width:auto;
        float: left;
        z-index: 9999;
    }
    .navigation li a{
        display: inline-block;
        height:40px;
        line-height: 40px;
        padding:0 11px;
        color:#fff;
        font-size: 16px;
    }
    .navigation li.hover ul{
        display: block;
    }
    .navigation li.current .nav-lv1,
    .navigation li.hover .nav-lv1,
    .navigation li .nav-lv1:hover{
        background: #528e01;
    }
    .navigation ul{
        display: none;
        background: #F9F9F9;
        border:1px solid #d8d8d8;
        color:#666;
        padding:8px 0;
        position: absolute;
        left:0;
        top:40px;
        /*width:105px;*/
        z-index: 999;
        border-radius: 0 0 4px 4px;
    }/*
.navigation ul.xf{
    width:90px;
}
.navigation ul.sydc{
    width:125px;
}*/
    .navigation ul li{
        float: none;
    }
    .navigation ul li a{
        display: block;
        color: #666;
        padding:0 25px 0 20px;
        line-height: 33px;
        height:33px;
        font-size: 14px;
        white-space:nowrap;
    }
    .navigation ul li a:hover{
        color: #528e01;
    }
    .navigation ul li.hr{
        height:0px;
        padding:0;
        margin:5px 10px;
        border-top:1px solid #ccc;
    }
    .navigation ul li.nav-triangle{
        position: absolute;
        left:0;
        top:-12px;
        width:0;
        height:0;
        padding:0;
        margin:0;
        border:6px solid transparent;
        border-bottom:6px solid #d8d8d8;
    }
    .navigation ul li.nav-triangle span{
        position: absolute;
        left:-6px;
        top:-4px;
        width:0;
        height:0;
        padding:0;
        margin:0;
        border:6px solid transparent;
        border-bottom:6px solid #F9F9F9;
    }

    /* 登录 */
    .nav-right{
        float: right;
        font-size: 14px;
    }
    .nav-right a{
        color: #fff;
    }
    .userbox{
        float: left;
        position: relative;
        /* margin-right: 20px; */
        display: inline-block;
        height:40px;
        line-height: 40px;
        width: 100px;
        padding:0 10px 0px 32px;
    }
    .nav-right .hover{
        background: #528e01;
    }
    .nav-right .hover ul{
        display: block;
    }
    .userbox a{
        margin-left:2px;
    }
    .userbox .icon-user{
        position: absolute;
        top:4px;
        left:0;
    }
    .userbox .triangle-down{
        border:3px solid #62AB00;
        border-top: 3px solid #fff;
        position: absolute;
        right:5px;
        top:18px;
    }
    .userbox .triangle-up{
        border:3px solid #528e01;
        border-bottom: 3px solid #fff;
        position: absolute;
        right:5px;
        top:18px;
    }
    .userbox .icon-point{
        text-align: center;
        font-size: 10px;
        line-height: 32px;
        position: absolute;
        color: #fff;
        right:10px;
        top:4px;
    }
    .userbox ul{
        display: none;
        position: absolute;
        z-index: 999;
        left:0;
        top:40px;
        width:140px;
        border: 1px solid #d8d8d8;
        background-color: #f9f9f9;
        border-radius: 0 0 4px 4px;
        padding: 8px 0;
    }
    .userbox ul li{
        padding: 0 0 0 28px;
    }
    .userbox ul li.hr{
        margin:8px 20px;
        border-top:1px solid #CCC;
    }
    .userbox ul a{
        font-size: 12px;
        margin:0;
        display: block;
        width:100%;
        line-height: 33px;
        height:33px;
        overflow: hidden;
        color:#666;
        cursor: pointer;
    }
    .userbox ul a em{
        margin-left: 5px;
        color: #f60;
    }
    .userbox ul li a:hover{
        color: #528e01;
    }

    .favorite{
        float: left;
        position: relative;
        width:100px;
        padding-left:32px;
        line-height: 40px;
    }
    .favorite .icon-favorite{
        position: absolute;
        left:0;
        top:4px;
    }
    .nav-right .download-app{
        float: left;
        display: inline-block;
        position: relative;
        width: 70px;
        padding-left:50px;
        height:27px;
        line-height: 27px;
        border-radius: 2px;
        margin-top:6px;
        background: #dce8cc;
        color: #666;
    }
    .nav-right .download-app:hover{
        background: #f2f6e3;
    }
    .download-app .icon-apple,.download-app .icon-android{
        position: absolute;
        top:3px;
    }
    .download-app .icon-apple{
        left:5px;
    }
    .download-app .icon-android{
        left:25px;
    }

    .userblock{
        padding: 2px 10px 2px 0px;
    }

    .userblock .link {
        width: 50px;
        overflow: hidden;
        height: 16px;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .personal-rent-dialog {
        z-index: 100;
        /*display: none;*/
        position: absolute;
        left: 0;
        top: 62px;
        text-align: center;
        width: 100%;
        padding: 25px 0 22px;
        border: 1px solid #e6e6e6;
        background: #fff;
        border-radius: 4px;
    }
    .personal-rent-dialog .arrow_box {
        position: absolute;
        top: -1px;
        left: 111px;
        background: #fff;
        border: 1px solid #e6e6e6;
    }
    .personal-rent-dialog .link {
        display: block;
        margin: 0 auto;
    }
    .personal-tel-dialog .tip {
        margin-top: 15px;
        font-size: 15px;
        color: #333;
        line-height: 1;
        font-weight: normal;
    }/**
*/
    .error-page{
        padding:86px 0 84px;
        width:566px;
        margin:0 auto;
    }
    .error-page img {
        width: 246px;
        height: 140px;
        float: left;
    }
    .error-info{
        display: inline-block;
        float:left;
        width:330px;
        color:#111;
        margin:50px 0 0 -15px;
        font-weight: 100;
    }
    .error-info>span{
        font-size:24px;
        color: #333;
        line-height: 24px;
    }
    .error-info p{
        color:#666;
        margin:14px 0;
        font-size: 18px;
        line-height:24px;
    }
    .error-info a{
        color: #1f64b2;
        padding-left: 10px;
        text-decoration: underline;
    }
    .error-info a:hover{
        color: #f60;

    }
    .recomm {
        width: 1140px;
        padding: 10px 20px;
        margin: 0 auto 30px;
        background-color: #F9F9F9;
        overflow: hidden;
    }
    .recomm span {
        display: inline-block;
        width: 68px;
        float: left;
        line-height: 24px;
        color: #333;
    }
    .recomm>li {
        display: block;
    }
    /*.recomm span {
        float: left;
    }*/
    .lists {
        display: inline-block;
        width: 1072px;
    }
    .lists a {
        color: #999;
        padding: 0;
        float: left;
        margin-right: 12px;
        width: 116px;
        line-height: 24px;
        text-decoration: none;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
    }
    .lists a:hover{
        text-decoration: underline;
    }
</style>

<div class="error-page clearfix">
    <img src="https://pages.anjukestatic.com/usersite/site/img/global/2/404.png">
    <div class="error-info">
        <span>抱歉,您要查看的页面丢失了</span>
        <p><a href="https://tj.zu.anjuke.com"><span></span>点击跳转到租房首页</a></p>
    </div>
</div>

<div class="recomm">
    <ul>
        <li>
            <span>推荐问答:</span>
            <ul class="lists">
                <li><a href="https://www.anjuke.com/ask/fl-fangjiahangqing/">房价行情</a></li><li><a href="https://www.anjuke.com/ask/fl-goufangjianyi/">购房建议</a></li><li><a href="https://www.anjuke.com/ask/fl-goufangzizhi/">购房资质</a></li><li><a href="https://www.anjuke.com/ask/fl-maifangfengxian/">买房风险</a></li><li><a href="https://www.anjuke.com/ask/fl-ershoufang/">二手房</a></li><li><a href="https://www.anjuke.com/ask/fl-xinfang/">新房</a></li><li><a href="https://www.anjuke.com/ask/fl-haiwiamaifang/">海外买房</a></li><li><a href="https://www.anjuke.com/ask/fl-fangwugujia/">房屋估价</a></li><li><a href="https://www.anjuke.com/ask/fl-maifangaliucheng/">卖房流程</a></li><li><a href="https://www.anjuke.com/ask/fl-chushoufangan/">出售方案</a></li><li><a href="https://www.anjuke.com/ask/fl-yezhufengxian/">业主风险</a></li><li><a href="https://www.anjuke.com/ask/fl-maijiumaixin/">卖旧买新</a></li><li><a href="https://www.anjuke.com/ask/fl-jiaoyiliucheng/">交易流程</a></li><li><a href="https://www.anjuke.com/ask/fl-daikuanlilv/">贷款利率</a></li><li><a href="https://www.anjuke.com/ask/fl-goufangcailiao/">购房材料</a></li><li><a href="https://www.anjuke.com/ask/fl-rongzishagndai/">融资商贷</a></li><li><a href="https://www.anjuke.com/ask/fl-shoufuyuegong/">首付月供</a></li><li><a href="https://www.anjuke.com/ask/fl-guohugengming/">过户更名</a></li><li><a href="https://www.anjuke.com/ask/fl-zhufanggongjijin/">住房公积金</a></li><li><a href="https://www.anjuke.com/ask/fl-shuifeijisuan/">税费计算</a></li><li><a href="https://www.anjuke.com/ask/fl-rengouqianyue/">认购签约</a></li><li><a href="https://www.anjuke.com/ask/fl-zijinjianguan/">资金监管</a></li><li><a href="https://www.anjuke.com/ask/fl-guohuliucheng/">过户流程</a></li><li><a href="https://www.anjuke.com/ask/fl-ruzhujiaojie/">入住交接</a></li><li><a href="https://www.anjuke.com/ask/fl-wuye/">物业</a></li><li><a href="https://www.anjuke.com/ask/fl-zufangzhunbei/">租房准备</a></li><li><a href="https://www.anjuke.com/ask/fl-zhufangshixiang/">租房注意事项</a></li><li><a href="https://www.anjuke.com/ask/fl-hezu/">合租</a></li><li><a href="https://www.anjuke.com/ask/fl-zhengzu/">整租</a></li><li><a href="https://www.anjuke.com/ask/fl-zufanghetong/">租房合同</a></li><li><a href="https://www.anjuke.com/ask/fl-zhuangxiu/">装修</a></li><li><a href="https://www.anjuke.com/ask/fl-tudi/">土地</a></li><li><a href="https://www.anjuke.com/ask/fl-chaiqian/">拆迁</a></li><li><a href="https://www.anjuke.com/ask/fl-fangchanzhengce/">房产政策</a></li><li><a href="https://www.anjuke.com/ask/fl-falvjiufen/">法律纠纷</a></li><li><a href="https://www.anjuke.com/ask/fl-qitab/">其他</a></li><li><a href="https://www.anjuke.com/ask/fl-shangpu/">商铺</a></li><li><a href="https://www.anjuke.com/ask/fl-xiezilou/">写字楼</a></li><li><a href="https://www.anjuke.com/ask/fl-tudichangfang/">土地厂房</a></li><li><a href="https://www.anjuke.com/ask/fl-chanyeyuanqu/">产业园区</a></li>                </ul>
        </li>
    </ul>
</div>
<div id="footer" class="footer">
    <div class="cp-mod">
        <a rel="nofollow" href="https://kfzx.anjuke.com/" target="_blank">客户服务中心</a>   
        邮箱：<a href="mailto:<EMAIL>" target="_blank" rel="nofollow"><EMAIL></a>   
        Copyright&nbsp;©&nbsp;2007-2025 www.anjuke.com All Rights Reserved   <a href="https://www.anjuke.com/sitemap-miibeian" rel="nofollow">ICP号：沪 B2-20090008</a>
    </div>
</div>
</body></html>